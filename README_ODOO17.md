# ABC Sales Analysis Reports - Odoo 17

## 📋 Description

Module d'analyse ABC des ventes pour Odoo 17 Community Edition. Ce module permet de générer des rapports d'analyse ABC, XYZ et ABC-XYZ combinés pour optimiser la gestion des stocks et des ventes.

## 🔄 Migration Odoo 18 → Odoo 17

Ce module a été migré d'Odoo 18 vers Odoo 17. <PERSON><PERSON><PERSON> le fichier `MIGRATION_ODOO17_CHANGELOG.md` pour les détails des modifications.

## ✨ Fonctionnalités

### 📊 Rapports disponibles

1. **ABC Sales Analysis Report**
   - Classification des produits selon le principe de Pareto (80/20)
   - Catégories A (20% - 80% du CA), B (30% - 15% du CA), C (50% - 5% du CA)

2. **XYZ Analysis Report**
   - Classification des stocks selon leur valeur
   - Catégories X (70% de la valeur), Y (20% de la valeur), Z (10% de la valeur)

3. **ABC-XYZ Combined Analysis Report**
   - Analyse combinée volume de ventes (ABC) et incertitude (XYZ)
   - 9 catégories possibles (AX, AY, AZ, BX, BY, BZ, CX, CY, CZ)

4. **ABC Sales Frequency Analysis Report**
   - Classification selon la fréquence des commandes
   - Aide à définir les stratégies de stockage et de retrait

### 🎯 Avantages

- **Meilleure prévision** : Classification basée sur la demande client
- **Négociation fournisseurs** : Focus sur les produits à fort impact
- **Tarification stratégique** : Optimisation des prix selon les catégories
- **Optimisation des stocks** : Réduction des coûts de stockage

## 🛠️ Installation

### Prérequis
- Odoo 17 Community Edition
- Modules dépendants : `sale_management`, `sale_stock`

### Étapes d'installation

1. **Copier le module**
   ```bash
   cp -r setu_abc_analysis_reports /path/to/odoo/addons/
   ```

2. **Redémarrer Odoo**
   ```bash
   sudo systemctl restart odoo
   ```

3. **Installer le module**
   - Aller dans Apps
   - Rechercher "ABC Sales Analysis Reports"
   - Cliquer sur "Install"

### Test d'installation

Exécutez le script de test fourni :
```bash
python test_installation.py
```

## 🚀 Utilisation

### Configuration ABC-XYZ

1. Aller dans **Ventes > Configuration > ABC XYZ Configuration**
2. Définir les pourcentages pour chaque catégorie :
   - **ABC** : A (0-40%), B (41-80%), C (81-100%)
   - **XYZ** : X (0-70%), Y (70-90%), Z (91-100%)
3. Cliquer sur "Update" pour sauvegarder

### Génération de rapports

1. **ABC Sales Analysis**
   - Aller dans **Ventes > Reporting > ABC Sales Analysis**
   - Sélectionner les dates, produits, catégories, entrepôts
   - Choisir le type d'analyse (All, High Sales, Medium Sales, Low Sales)
   - Cliquer sur "Excel Report", "View Data" ou "View Graph"

2. **XYZ Analysis**
   - Aller dans **Ventes > Reporting > Inventory XYZ Analysis**
   - Sélectionner les filtres souhaités
   - Générer le rapport

3. **ABC-XYZ Combined**
   - Aller dans **Ventes > Reporting > ABC-XYZ Analysis**
   - Configurer les paramètres d'analyse
   - Générer le rapport combiné

## 📁 Structure du module

```
setu_abc_analysis_reports/
├── __init__.py                 # Initialisation du module
├── __manifest__.py             # Manifeste Odoo 17
├── controllers/                # Contrôleurs web
│   ├── __init__.py
│   └── main.py                # Contrôleur de téléchargement Excel
├── data/                      # Données par défaut
│   └── data.xml              # Paramètres de configuration ABC/XYZ
├── db_function/              # Fonctions PostgreSQL
│   ├── get_abc_sales_analysis_data.sql
│   ├── get_abc_xyz_analysis_report.sql
│   ├── get_inventory_xyz_analysis_data.sql
│   └── ...
├── library/                  # Bibliothèque xlsxwriter
│   └── xlsxwriter/
├── security/                 # Droits d'accès
│   └── ir.model.access.csv
├── static/                   # Ressources statiques
│   └── description/
├── views/                    # Vues XML
│   ├── setu_abc_configuration.xml
│   ├── setu_abc_sales_analysis_report.xml
│   └── ...
└── wizard/                   # Assistants et modèles
    ├── __init__.py
    ├── setu_abc_configuration.py
    ├── setu_abc_sales_analysis_report.py
    └── ...
```

## 🔧 Développement

### Tests
```bash
# Test de syntaxe et structure
python test_installation.py

# Test d'installation Odoo
odoo -d test_db -i setu_abc_analysis_reports --test-enable
```

### Logs
Les logs sont disponibles dans le fichier de log d'Odoo :
```bash
tail -f /var/log/odoo/odoo.log
```

## 📞 Support

- **Développeur** : Setu Consulting Services Pvt. Ltd.
- **Email** : <EMAIL>
- **Site web** : https://www.setuconsulting.com

## 📄 Licence

OPL-1 (Odoo Proprietary License v1.0)

## 🔄 Historique des versions

- **********.0** : Migration vers Odoo 17
- **1.3** : Version Odoo 18 (originale)

---

**Note** : Ce module a été testé et validé pour Odoo 17 Community Edition.
