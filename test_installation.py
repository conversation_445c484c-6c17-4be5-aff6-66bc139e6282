#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script de test pour vérifier l'installation du module setu_abc_analysis_reports sur Odoo 17

Usage:
    python test_installation.py

Ce script vérifie:
1. La syntaxe des fichiers Python
2. La validité des fichiers XML
3. La structure du module
"""

import os
import sys
import ast
import xml.etree.ElementTree as ET
from pathlib import Path

def test_python_syntax():
    """Test la syntaxe de tous les fichiers Python du module"""
    print("🔍 Test de la syntaxe Python...")
    python_files = []
    
    # Recherche récursive des fichiers .py
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    errors = []
    for py_file in python_files:
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                content = f.read()
            ast.parse(content)
            print(f"  ✅ {py_file}")
        except SyntaxError as e:
            errors.append(f"  ❌ {py_file}: {e}")
            print(f"  ❌ {py_file}: {e}")
        except Exception as e:
            errors.append(f"  ⚠️  {py_file}: {e}")
            print(f"  ⚠️  {py_file}: {e}")
    
    return errors

def test_xml_syntax():
    """Test la validité de tous les fichiers XML du module"""
    print("\n🔍 Test de la syntaxe XML...")
    xml_files = []
    
    # Recherche récursive des fichiers .xml
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.xml'):
                xml_files.append(os.path.join(root, file))
    
    errors = []
    for xml_file in xml_files:
        try:
            ET.parse(xml_file)
            print(f"  ✅ {xml_file}")
        except ET.ParseError as e:
            errors.append(f"  ❌ {xml_file}: {e}")
            print(f"  ❌ {xml_file}: {e}")
        except Exception as e:
            errors.append(f"  ⚠️  {xml_file}: {e}")
            print(f"  ⚠️  {xml_file}: {e}")
    
    return errors

def test_manifest():
    """Test la validité du fichier __manifest__.py"""
    print("\n🔍 Test du fichier __manifest__.py...")
    
    if not os.path.exists('__manifest__.py'):
        print("  ❌ Fichier __manifest__.py manquant")
        return ["Fichier __manifest__.py manquant"]
    
    try:
        with open('__manifest__.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Parse le contenu comme du Python
        parsed = ast.parse(content)
        
        # Vérifie que c'est un dictionnaire
        if not (len(parsed.body) == 1 and isinstance(parsed.body[0], ast.Expr) 
                and isinstance(parsed.body[0].value, ast.Dict)):
            print("  ❌ Le __manifest__.py doit contenir un dictionnaire")
            return ["Le __manifest__.py doit contenir un dictionnaire"]
        
        # Exécute le contenu pour obtenir le dictionnaire
        manifest_dict = eval(content)
        
        # Vérifications spécifiques
        required_keys = ['name', 'version', 'depends', 'data']
        missing_keys = [key for key in required_keys if key not in manifest_dict]
        
        if missing_keys:
            error = f"Clés manquantes dans __manifest__.py: {missing_keys}"
            print(f"  ❌ {error}")
            return [error]
        
        # Vérifie la version Odoo 17
        version = manifest_dict.get('version', '')
        if not version.startswith('17.0'):
            error = f"Version incorrecte: {version} (doit commencer par '17.0')"
            print(f"  ❌ {error}")
            return [error]
        
        print("  ✅ __manifest__.py valide")
        print(f"    - Nom: {manifest_dict.get('name')}")
        print(f"    - Version: {manifest_dict.get('version')}")
        print(f"    - Dépendances: {manifest_dict.get('depends')}")
        
        return []
        
    except Exception as e:
        error = f"Erreur lors de l'analyse du __manifest__.py: {e}"
        print(f"  ❌ {error}")
        return [error]

def test_module_structure():
    """Test la structure du module"""
    print("\n🔍 Test de la structure du module...")
    
    required_files = ['__init__.py', '__manifest__.py']
    required_dirs = ['wizard', 'views', 'security', 'data']
    
    errors = []
    
    # Vérification des fichiers requis
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            error = f"Fichier manquant: {file}"
            errors.append(error)
            print(f"  ❌ {error}")
    
    # Vérification des dossiers requis
    for dir in required_dirs:
        if os.path.isdir(dir):
            print(f"  ✅ {dir}/")
        else:
            error = f"Dossier manquant: {dir}/"
            errors.append(error)
            print(f"  ❌ {error}")
    
    return errors

def main():
    """Fonction principale"""
    print("🚀 Test d'installation du module setu_abc_analysis_reports pour Odoo 17")
    print("=" * 70)
    
    all_errors = []
    
    # Tests
    all_errors.extend(test_module_structure())
    all_errors.extend(test_manifest())
    all_errors.extend(test_python_syntax())
    all_errors.extend(test_xml_syntax())
    
    # Résumé
    print("\n" + "=" * 70)
    print("📊 RÉSUMÉ DES TESTS")
    
    if not all_errors:
        print("🎉 TOUS LES TESTS SONT PASSÉS!")
        print("✅ Le module semble prêt pour l'installation sur Odoo 17")
        return 0
    else:
        print(f"❌ {len(all_errors)} ERREUR(S) DÉTECTÉE(S):")
        for error in all_errors:
            print(f"   • {error}")
        print("\n⚠️  Veuillez corriger ces erreurs avant l'installation")
        return 1

if __name__ == "__main__":
    sys.exit(main())
