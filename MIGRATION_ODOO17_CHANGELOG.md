# Migration Odoo 18 → Odoo 17 - Changelog

## Module: setu_abc_analysis_reports

### Date de conversion: 2025-08-22

---

## Résumé des modifications

Ce document détaille toutes les modifications apportées au module `setu_abc_analysis_reports` pour le rendre compatible avec Odoo 17 Community.

### 1. Fichier __manifest__.py

**Modification:**
- Version changée de `'1.3'` vers `'17.0.1.0.0'`

**Justification:**
- Adaptation au format de versioning d'Odoo 17

### 2. Mod<PERSON><PERSON> Python - Décorateur @api.model_create_multi

**Fichiers modifiés:**
- `wizard/setu_abc_sales_analysis_bi_report.py`
- `wizard/setu_abc_sales_frequency_analysis_bi_report.py`
- `wizard/setu_abc_xyz_analysis_bi_report.py`
- `wizard/setu_inventory_xyz_analysis_bi_report.py`

**Modification:**
- Remplacement de `@api.model_create_multi` par `@api.model`

**Justification:**
- Le décorateur `@api.model_create_multi` n'existe pas dans Odoo 17
- D'après le changelog du module, ce décorateur causait des DeprecationWarning dans Odoo 18

### 3. Fichiers XML - Vues Liste

**Fichiers modifiés:**
- `wizard/setu_abc_sales_analysis_bi_report_views.xml`
- `wizard/setu_abc_sales_frequency_analysis_bi_report_views.xml`
- `wizard/setu_abc_xyz_analysis_bi_report_views.xml`
- `wizard/setu_invnetory_xyz_analysis_bi_report_views.xml`

**Modification:**
- Remplacement de `<list>` par `<tree>` et `</list>` par `</tree>`

**Justification:**
- Dans Odoo 17, les vues liste utilisent l'élément `<tree>` au lieu de `<list>`

### 4. Fichiers XML - Attributs de champs

**Fichiers modifiés:**
- `wizard/setu_abc_sales_analysis_bi_report_views.xml`
- `wizard/setu_abc_sales_frequency_analysis_bi_report_views.xml`
- `wizard/setu_abc_xyz_analysis_bi_report_views.xml`

**Modification:**
- Remplacement de `column_invisible="1"` par `invisible="1"`

**Justification:**
- L'attribut `column_invisible` n'existe pas dans Odoo 17, remplacé par `invisible`

### 5. Actions de fenêtre

**Fichier modifié:**
- `views/setu_abc_configuration.xml`

**Modification:**
- Suppression de l'attribut `binding_view_types="form"`

**Justification:**
- L'attribut `binding_view_types` n'existe pas dans Odoo 17

---

## Éléments vérifiés et confirmés compatibles

### Dépendances
- `sale_management` ✓
- `sale_stock` ✓

### Imports Python
- Tous les imports sont compatibles avec Odoo 17 ✓

### Contrôleurs
- Le contrôleur `controllers/main.py` est compatible ✓

### Fichiers de sécurité
- `security/ir.model.access.csv` est compatible ✓

### Fichiers de données
- `data/data.xml` est compatible ✓

### Fonctions SQL
- Toutes les fonctions PostgreSQL dans `db_function/` sont compatibles ✓

---

## Tests recommandés

1. **Installation du module:**
   ```bash
   odoo -d test_db -i setu_abc_analysis_reports
   ```

2. **Tests fonctionnels:**
   - Vérifier l'accès aux menus
   - Tester la génération de rapports ABC
   - Tester la génération de rapports XYZ
   - Tester la génération de rapports ABC-XYZ combinés
   - Vérifier l'export Excel
   - Tester les vues graphiques

3. **Tests de configuration:**
   - Accéder à la configuration ABC-XYZ
   - Modifier les pourcentages
   - Vérifier la sauvegarde des paramètres

---

## Statut de la migration

✅ **TERMINÉ** - Le module est prêt pour Odoo 17 Community

### Prochaines étapes recommandées:
1. Tester l'installation sur une instance Odoo 17 de développement
2. Effectuer des tests fonctionnels complets
3. Valider avec des données de test
4. Déployer en production après validation complète
